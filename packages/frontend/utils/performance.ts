'use client';

import { useCallback, useRef, useEffect } from 'react';

// Throttle function for high-frequency events
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;

  return (...args: Parameters<T>) => {
    const currentTime = Date.now();

    if (currentTime - lastExecTime > delay) {
      func(...args);
      lastExecTime = currentTime;
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      timeoutId = setTimeout(() => {
        func(...args);
        lastExecTime = Date.now();
      }, delay - (currentTime - lastExecTime));
    }
  };
}

// Debounce function for events that should only fire after a pause
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
}

// RequestAnimationFrame-based throttling for smooth animations
export function rafThrottle<T extends (...args: any[]) => any>(
  func: T
): (...args: Parameters<T>) => void {
  let rafId: number | null = null;
  let latestArgs: Parameters<T> | null = null;

  return (...args: Parameters<T>) => {
    latestArgs = args;

    if (rafId === null) {
      rafId = requestAnimationFrame(() => {
        if (latestArgs) {
          func(...latestArgs);
        }
        rafId = null;
        latestArgs = null;
      });
    }
  };
}

// React hook for throttled callbacks
export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): (...args: Parameters<T>) => void {
  const throttledFn = useRef<(...args: Parameters<T>) => void>(null as any);

  useEffect(() => {
    throttledFn.current = throttle(callback, delay);
  }, [callback, delay, ...deps]);

  return useCallback((...args: Parameters<T>) => {
    throttledFn.current?.(...args);
  }, []);
}

// React hook for debounced callbacks
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): (...args: Parameters<T>) => void {
  const debouncedFn = useRef<(...args: Parameters<T>) => void>(null as any);

  useEffect(() => {
    debouncedFn.current = debounce(callback, delay);
  }, [callback, delay, ...deps]);

  return useCallback((...args: Parameters<T>) => {
    debouncedFn.current?.(...args);
  }, []);
}

// React hook for RAF-throttled callbacks
export function useRafThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList = []
): (...args: Parameters<T>) => void {
  const rafThrottledFn = useRef<(...args: Parameters<T>) => void>(null as any);

  useEffect(() => {
    rafThrottledFn.current = rafThrottle(callback);
  }, [callback, ...deps]);

  return useCallback((...args: Parameters<T>) => {
    rafThrottledFn.current?.(...args);
  }, []);
}

// Batch state updates to prevent excessive re-renders
export class BatchUpdater<T> {
  private updates: T[] = [];
  private timeoutId: NodeJS.Timeout | null = null;
  private callback: (updates: T[]) => void;
  private delay: number;

  constructor(callback: (updates: T[]) => void, delay: number = 16) {
    this.callback = callback;
    this.delay = delay;
  }

  add(update: T): void {
    this.updates.push(update);

    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }

    this.timeoutId = setTimeout(() => {
      if (this.updates.length > 0) {
        this.callback([...this.updates]);
        this.updates = [];
      }
      this.timeoutId = null;
    }, this.delay);
  }

  flush(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    if (this.updates.length > 0) {
      this.callback([...this.updates]);
      this.updates = [];
    }
  }

  clear(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    this.updates = [];
  }
}

// Performance measurement utilities
export class PerformanceProfiler {
  private measurements: Map<string, number[]> = new Map();

  start(label: string): () => void {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;

      if (!this.measurements.has(label)) {
        this.measurements.set(label, []);
      }

      this.measurements.get(label)!.push(duration);
    };
  }

  getStats(label: string): { avg: number; min: number; max: number; count: number } | null {
    const measurements = this.measurements.get(label);
    if (!measurements || measurements.length === 0) {
      return null;
    }

    const avg = measurements.reduce((sum, val) => sum + val, 0) / measurements.length;
    const min = Math.min(...measurements);
    const max = Math.max(...measurements);

    return { avg, min, max, count: measurements.length };
  }

  clear(label?: string): void {
    if (label) {
      this.measurements.delete(label);
    } else {
      this.measurements.clear();
    }
  }

  getAllStats(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const stats: Record<string, { avg: number; min: number; max: number; count: number }> = {};

    for (const [label] of this.measurements) {
      const stat = this.getStats(label);
      if (stat) {
        stats[label] = stat;
      }
    }

    return stats;
  }
}

// Global performance profiler instance
export const globalProfiler = new PerformanceProfiler();

// React hook for performance profiling
export function usePerformanceProfiler(label: string, enabled: boolean = process.env.NODE_ENV === 'development') {
  const profilerRef = useRef<PerformanceProfiler>(globalProfiler);

  const startMeasurement = useCallback(() => {
    if (!enabled) return () => { };
    return profilerRef.current.start(label);
  }, [label, enabled]);

  const getStats = useCallback(() => {
    return profilerRef.current.getStats(label);
  }, [label]);

  return { startMeasurement, getStats };
}

// Utility to detect performance issues
export function detectPerformanceIssues() {
  const stats = globalProfiler.getAllStats();
  const issues: string[] = [];

  Object.entries(stats).forEach(([label, stat]) => {
    if (stat.avg > 16) { // More than one frame at 60fps
      issues.push(`${label}: Average ${stat.avg.toFixed(2)}ms (target: <16ms)`);
    }

    if (stat.max > 100) { // Blocking operations
      issues.push(`${label}: Max ${stat.max.toFixed(2)}ms (blocking threshold: 100ms)`);
    }
  });

  return issues;
}
