'use client';

import React, { useState } from 'react';
import * as fabric from 'fabric';
import MangaCanvas from '@/components/canvas/MangaCanvas';

const CanvasImageTest: React.FC = () => {
  const [testImageUrl, setTestImageUrl] = useState<string>('');
  const [canvasReady, setCanvasReady] = useState<boolean>(false);

  // Test with a sample image URL
  const handleTestImage = () => {
    // Use a test image URL - you can replace this with any valid image URL
    const sampleImageUrl = 'https://via.placeholder.com/800x600/0066cc/ffffff?text=Test+Image';
    setTestImageUrl(sampleImageUrl);
    console.log('Setting test image URL:', sampleImageUrl);
  };

  // Test with backend API URL (if you have a page uploaded)
  const handleTestBackendImage = () => {
    // Example backend image URL - replace with actual page ID
    const backendImageUrl = 'http://localhost:8000/api/v1/projects/pages/1c912f92-1351-4108-9f81-43d55704bd8b/image';
    setTestImageUrl(backendImageUrl);
    console.log('Setting backend image URL:', backendImageUrl);
  };

  const handleCanvasReady = (canvas: fabric.Canvas) => {
    console.log('Canvas ready in test component:', canvas);
    setCanvasReady(true);
  };

  const handleClearImage = () => {
    setTestImageUrl('');
    console.log('Clearing image URL');
  };

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Canvas Image Loading Test</h2>

      <div className="space-y-2">
        <div>
          <strong>Canvas Ready:</strong> {canvasReady ? '✅ Yes' : '❌ No'}
        </div>
        <div>
          <strong>Current Image URL:</strong> {testImageUrl || 'None'}
        </div>
      </div>

      <div className="space-x-2">
        <button
          onClick={handleTestImage}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Load Test Image
        </button>
        <button
          onClick={handleTestBackendImage}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Load Backend Image
        </button>
        <button
          onClick={handleClearImage}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Clear Image
        </button>
      </div>

      <div className="border-2 border-dashed border-gray-300 p-4">
        <h3 className="text-lg font-semibold mb-2">Canvas:</h3>
        <MangaCanvas
          imageUrl={testImageUrl}
          width={600}
          height={400}
          onCanvasReady={handleCanvasReady}
          className="border-2 border-blue-300"
        />
      </div>

      <div className="text-sm text-gray-600">
        <h4 className="font-semibold">Instructions:</h4>
        <ol className="list-decimal list-inside space-y-1">
          <li>Click "Load Test Image" to load a sample image</li>
          <li>Check the browser console for debug logs</li>
          <li>Verify that the image appears on the canvas</li>
          <li>Click "Clear Image" to remove the image</li>
        </ol>
      </div>
    </div>
  );
};

export default CanvasImageTest;
