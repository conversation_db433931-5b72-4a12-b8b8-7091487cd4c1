'use client';

import React, { memo, useCallback, useMemo } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useCanvasStore } from '@/store/canvasStore';
import { useOptimizedCanvas } from '@/hooks/useOptimizedCanvas';
import { CanvasTextRegion, CanvasTool } from '@/types/canvas';

interface OptimizedMangaCanvasProps {
  imageUrl?: string;
  width?: number;
  height?: number;
  onCanvasReady?: (canvas: fabric.Canvas) => void;
  onRegionCreate?: (region: Partial<CanvasTextRegion>) => void;
  onRegionUpdate?: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  onRegionDelete?: (regionId: string) => void;
  onRegionSelect?: (regionId: string) => void;
  className?: string;
}

// Memoized canvas component to prevent unnecessary re-renders
const OptimizedMangaCanvas = memo<OptimizedMangaCanvasProps>(({
  imageUrl,
  width = 800,
  height = 600,
  onCanvasReady,
  onRegionCreate,
  onRegionUpdate,
  onRegionDelete,
  onRegionSelect,
  className = ''
}) => {
  // Use selective subscriptions to prevent unnecessary re-renders
  const selectedTool = useCanvasStore((state) => state.selectedTool);
  
  // Memoize canvas options to prevent recreation on every render
  const canvasOptions = useMemo(() => ({
    onRegionCreate,
    onRegionUpdate,
    onRegionDelete,
    onRegionSelect
  }), [onRegionCreate, onRegionUpdate, onRegionDelete, onRegionSelect]);
  
  // Use optimized canvas hook
  const {
    initializeCanvas,
    loadImage,
    canvasRef
  } = useOptimizedCanvas(canvasOptions);
  
  // Memoized canvas initialization to prevent recreation
  const handleCanvasReady = useCallback((canvas: fabric.Canvas) => {
    initializeCanvas(canvas);
    onCanvasReady?.(canvas);
    
    // Load image if provided
    if (imageUrl) {
      loadImage(imageUrl);
    }
  }, [initializeCanvas, onCanvasReady, loadImage, imageUrl]);
  
  // Memoized canvas element to prevent recreation
  const canvasElement = useMemo(() => (
    <canvas
      ref={(el) => {
        if (el && !canvasRef.current) {
          const fabricCanvas = new fabric.Canvas(el, {
            width,
            height,
            selection: selectedTool === CanvasTool.SELECT,
            preserveObjectStacking: true,
            renderOnAddRemove: false, // Manual render control for performance
            skipTargetFind: false,
            perPixelTargetFind: true,
            targetFindTolerance: 5
          });
          
          handleCanvasReady(fabricCanvas);
        }
      }}
      width={width}
      height={height}
      className={`border border-gray-300 ${className}`}
    />
  ), [width, height, selectedTool, className, handleCanvasReady, canvasRef]);
  
  return (
    <div className="relative">
      {canvasElement}
    </div>
  );
});

OptimizedMangaCanvas.displayName = 'OptimizedMangaCanvas';

export default OptimizedMangaCanvas;

// Performance-optimized canvas controls component
interface CanvasControlsProps {
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  onToolChange: (tool: CanvasTool) => void;
  className?: string;
}

export const CanvasControls = memo<CanvasControlsProps>(({
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onToolChange,
  className = ''
}) => {
  // Use selective subscription for current tool
  const selectedTool = useCanvasStore((state) => state.selectedTool);
  
  // Memoize tool buttons to prevent recreation
  const toolButtons = useMemo(() => [
    { tool: CanvasTool.SELECT, label: 'Select', icon: '↖️' },
    { tool: CanvasTool.PAN, label: 'Pan', icon: '✋' },
    { tool: CanvasTool.TEXT_REGION, label: 'Text Region', icon: '📝' }
  ], []);
  
  // Memoized button click handlers
  const handleToolClick = useCallback((tool: CanvasTool) => {
    onToolChange(tool);
  }, [onToolChange]);
  
  return (
    <div className={`flex flex-col gap-2 p-2 bg-white rounded-lg shadow-lg ${className}`}>
      {/* Zoom Controls */}
      <div className="flex flex-col gap-1">
        <button
          onClick={onZoomIn}
          className="px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          type="button"
        >
          Zoom In
        </button>
        <button
          onClick={onZoomOut}
          className="px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          type="button"
        >
          Zoom Out
        </button>
        <button
          onClick={onResetZoom}
          className="px-3 py-2 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          type="button"
        >
          Reset
        </button>
      </div>
      
      {/* Tool Selection */}
      <div className="flex flex-col gap-1 border-t pt-2">
        {toolButtons.map(({ tool, label, icon }) => (
          <button
            key={tool}
            onClick={() => handleToolClick(tool)}
            className={`px-3 py-2 text-sm rounded transition-colors ${
              selectedTool === tool
                ? 'bg-green-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
            type="button"
          >
            {icon} {label}
          </button>
        ))}
      </div>
    </div>
  );
});

CanvasControls.displayName = 'CanvasControls';

// Performance-optimized viewport display component
interface ViewportDisplayProps {
  className?: string;
}

export const ViewportDisplay = memo<ViewportDisplayProps>(({ className = '' }) => {
  // Use shallow selector for viewport to prevent unnecessary re-renders
  const viewport = useCanvasStore(useShallow((state) => state.viewport));
  
  // Memoize formatted values
  const formattedValues = useMemo(() => ({
    zoom: `${Math.round(viewport.zoom * 100)}%`,
    panX: Math.round(viewport.panX),
    panY: Math.round(viewport.panY)
  }), [viewport.zoom, viewport.panX, viewport.panY]);
  
  return (
    <div className={`text-xs text-gray-600 bg-white p-2 rounded shadow ${className}`}>
      <div>Zoom: {formattedValues.zoom}</div>
      <div>Pan: {formattedValues.panX}, {formattedValues.panY}</div>
    </div>
  );
});

ViewportDisplay.displayName = 'ViewportDisplay';
