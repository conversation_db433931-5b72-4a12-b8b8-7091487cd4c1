'use client';

import React, { memo, useCallback, useMemo } from 'react';
import { useCanvasStore } from '@/store/canvasStore';
import MangaCanvas, { CanvasControls, ViewportDisplay } from '@/components/canvas/MangaCanvas';
import { CanvasTextRegion, CanvasTool } from '@/types/canvas';
import { TextRegionType, TranslationStatus } from '@/types/api';

interface TranslationLayoutProps {
  imageUrl?: string;
  className?: string;
}

// Main optimized layout component
const TranslationLayout = memo<TranslationLayoutProps>(({
  imageUrl,
  className = ''
}) => {
  // Use selective subscriptions to prevent unnecessary re-renders
  const textRegions = useCanvasStore((state) => state.textRegions);
  const selectedRegionIds = useCanvasStore((state) => state.selectedRegionIds);

  // Get store actions
  const { addRegion, updateRegion, removeRegion, selectRegion, zoomIn, zoomOut, resetZoom, setTool } = useCanvasStore();

  // Memoized event handlers to prevent recreation on every render
  const handleRegionCreate = useCallback((region: Partial<CanvasTextRegion>) => {
    const newRegion: CanvasTextRegion = {
      id: `region-${Date.now()}`,
      page_id: '',
      x: region.x || 0,
      y: region.y || 0,
      width: region.width || 0.1,
      height: region.height || 0.1,
      original_text: '',
      translated_text: '',
      font_family: 'Arial',
      font_size: 14,
      font_color: '#000000',
      background_color: 'transparent',
      region_type: TextRegionType.OTHER,
      translation_status: TranslationStatus.PENDING,
      isSelected: false,
      isEditing: false,
      borderColor: '#3b82f6',
      fillOpacity: 0.2,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...region
    };
    addRegion(newRegion);
  }, [addRegion]);

  const handleRegionUpdate = useCallback((regionId: string, updates: Partial<CanvasTextRegion>) => {
    updateRegion(regionId, updates);
  }, [updateRegion]);

  const handleRegionDelete = useCallback((regionId: string) => {
    removeRegion(regionId);
  }, [removeRegion]);

  const handleRegionSelect = useCallback((regionId: string) => {
    selectRegion(regionId);
  }, [selectRegion]);

  const handleToolChange = useCallback((tool: CanvasTool) => {
    setTool(tool);
  }, [setTool]);

  // Memoized canvas props to prevent recreation
  const canvasProps = useMemo(() => ({
    imageUrl,
    width: 800,
    height: 600,
    onRegionCreate: handleRegionCreate,
    onRegionUpdate: handleRegionUpdate,
    onRegionDelete: handleRegionDelete,
    onRegionSelect: handleRegionSelect,
    className: 'shadow-lg'
  }), [imageUrl, handleRegionCreate, handleRegionUpdate, handleRegionDelete, handleRegionSelect]);

  // Memoized controls props
  const controlsProps = useMemo(() => ({
    onZoomIn: zoomIn,
    onZoomOut: zoomOut,
    onResetZoom: resetZoom,
    onToolChange: handleToolChange
  }), [zoomIn, zoomOut, resetZoom, handleToolChange]);

  return (
    <div className={`flex h-screen bg-gray-100 ${className}`}>
      {/* Left Sidebar - Text Regions List */}
      <div className="w-80 bg-white border-r border-gray-300 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800">Text Regions</h2>
        </div>
        <div className="flex-1 overflow-y-auto">
          <TextRegionsList
            regions={textRegions}
            selectedRegionIds={selectedRegionIds}
            onRegionSelect={handleRegionSelect}
            onRegionUpdate={handleRegionUpdate}
            onRegionDelete={handleRegionDelete}
          />
        </div>
      </div>

      {/* Main Canvas Area */}
      <div className="flex-1 flex flex-col">
        <div className="flex-1 p-4 flex items-center justify-center relative">
          <MangaCanvas {...canvasProps} />

          {/* Floating Controls */}
          <div className="absolute bottom-4 left-4">
            <CanvasControls {...controlsProps} />
          </div>

          {/* Viewport Display */}
          <div className="absolute top-4 right-4">
            <ViewportDisplay />
          </div>
        </div>
      </div>

      {/* Right Sidebar - Region Editor */}
      <div className="w-80 bg-white border-l border-gray-300">
        <RegionEditor
          selectedRegion={textRegions.find(r => selectedRegionIds.includes(r.id))}
          onRegionUpdate={handleRegionUpdate}
          onRegionDelete={handleRegionDelete}
        />
      </div>
    </div>
  );
});

TranslationLayout.displayName = 'TranslationLayout';

// Optimized text regions list component
interface TextRegionsListProps {
  regions: CanvasTextRegion[];
  selectedRegionIds: string[];
  onRegionSelect: (regionId: string) => void;
  onRegionUpdate: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  onRegionDelete: (regionId: string) => void;
}

const TextRegionsList = memo<TextRegionsListProps>(({
  regions,
  selectedRegionIds,
  onRegionSelect,
  onRegionUpdate,
  onRegionDelete
}) => {
  // Memoize region items to prevent recreation
  const regionItems = useMemo(() =>
    regions.map((region) => (
      <TextRegionItem
        key={region.id}
        region={region}
        isSelected={selectedRegionIds.includes(region.id)}
        onSelect={() => onRegionSelect(region.id)}
        onUpdate={(updates) => onRegionUpdate(region.id, updates)}
        onDelete={() => onRegionDelete(region.id)}
      />
    )), [regions, selectedRegionIds, onRegionSelect, onRegionUpdate, onRegionDelete]
  );

  if (regions.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        No text regions yet. Use the Text Region tool to create one.
      </div>
    );
  }

  return (
    <div className="p-2 space-y-2">
      {regionItems}
    </div>
  );
});

TextRegionsList.displayName = 'TextRegionsList';

// Optimized individual region item component
interface TextRegionItemProps {
  region: CanvasTextRegion;
  isSelected: boolean;
  onSelect: () => void;
  onUpdate: (updates: Partial<CanvasTextRegion>) => void;
  onDelete: () => void;
}

const TextRegionItem = memo<TextRegionItemProps>(({
  region,
  isSelected,
  onSelect,
  onUpdate,
  onDelete
}) => {
  const handleTextChange = useCallback((field: 'original_text' | 'translated_text', value: string) => {
    onUpdate({ [field]: value });
  }, [onUpdate]);

  return (
    <div
      className={`p-3 border rounded cursor-pointer transition-colors ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
        }`}
      onClick={onSelect}
    >
      <div className="text-sm font-medium text-gray-700 mb-2">
        Region {region.id.split('-')[1]}
      </div>

      <div className="space-y-2">
        <div>
          <label className="text-xs text-gray-500">Original Text</label>
          <input
            type="text"
            value={region.original_text || ''}
            onChange={(e) => handleTextChange('original_text', e.target.value)}
            className="w-full text-xs p-1 border border-gray-200 rounded"
            placeholder="Original text..."
            onClick={(e) => e.stopPropagation()}
          />
        </div>

        <div>
          <label className="text-xs text-gray-500">Translated Text</label>
          <input
            type="text"
            value={region.translated_text || ''}
            onChange={(e) => handleTextChange('translated_text', e.target.value)}
            className="w-full text-xs p-1 border border-gray-200 rounded"
            placeholder="Translated text..."
            onClick={(e) => e.stopPropagation()}
          />
        </div>
      </div>

      <button
        onClick={(e) => {
          e.stopPropagation();
          onDelete();
        }}
        className="mt-2 text-xs text-red-600 hover:text-red-800"
      >
        Delete
      </button>
    </div>
  );
});

TextRegionItem.displayName = 'TextRegionItem';

// Optimized region editor component
interface RegionEditorProps {
  selectedRegion?: CanvasTextRegion;
  onRegionUpdate: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  onRegionDelete: (regionId: string) => void;
}

const RegionEditor = memo<RegionEditorProps>(({
  selectedRegion,
  onRegionUpdate,
  onRegionDelete
}) => {
  const handleUpdate = useCallback((field: string, value: any) => {
    if (selectedRegion) {
      onRegionUpdate(selectedRegion.id, { [field]: value });
    }
  }, [selectedRegion, onRegionUpdate]);

  if (!selectedRegion) {
    return (
      <div className="p-4 text-center text-gray-500">
        Select a text region to edit
      </div>
    );
  }

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">Edit Region</h3>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Font Size
          </label>
          <input
            type="number"
            value={selectedRegion.font_size || 14}
            onChange={(e) => handleUpdate('font_size', parseInt(e.target.value))}
            className="w-full p-2 border border-gray-300 rounded"
            min="8"
            max="72"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Font Color
          </label>
          <input
            type="color"
            value={selectedRegion.font_color || '#000000'}
            onChange={(e) => handleUpdate('font_color', e.target.value)}
            className="w-full h-10 border border-gray-300 rounded"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Background Color
          </label>
          <input
            type="color"
            value={selectedRegion.background_color === 'transparent' ? '#ffffff' : selectedRegion.background_color || '#ffffff'}
            onChange={(e) => handleUpdate('background_color', e.target.value)}
            className="w-full h-10 border border-gray-300 rounded"
          />
        </div>

        <button
          onClick={() => onRegionDelete(selectedRegion.id)}
          className="w-full py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          Delete Region
        </button>
      </div>
    </div>
  );
});

RegionEditor.displayName = 'RegionEditor';

export default TranslationLayout;
