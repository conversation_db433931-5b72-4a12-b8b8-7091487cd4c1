'use client';

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { CanvasState, CanvasTool, CanvasTextRegion, CanvasImageInfo } from '@/types/canvas';

// Separate stores for different concerns to prevent unnecessary re-renders
interface CanvasStore {
  // Canvas viewport state (high-frequency updates)
  viewport: {
    zoom: number;
    panX: number;
    panY: number;
  };
  
  // Canvas interaction state
  interaction: {
    isDrawing: boolean;
    selectedTool: CanvasTool;
    isDragging: boolean;
    isPanning: boolean;
  };
  
  // Image information (low-frequency updates)
  image: CanvasImageInfo | null;
  
  // Actions for viewport (optimized for high-frequency updates)
  setViewport: (viewport: Partial<CanvasStore['viewport']>) => void;
  
  // Actions for interaction state
  setInteraction: (interaction: Partial<CanvasStore['interaction']>) => void;
  
  // Actions for image
  setImage: (image: CanvasImageInfo | null) => void;
  
  // Utility actions
  resetViewport: () => void;
  zoomIn: () => void;
  zoomOut: () => void;
}

interface TextRegionsStore {
  // Text regions state
  regions: CanvasTextRegion[];
  selectedRegionIds: string[];
  
  // Actions
  addRegion: (region: CanvasTextRegion) => void;
  updateRegion: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  removeRegion: (regionId: string) => void;
  selectRegion: (regionId: string) => void;
  selectMultipleRegions: (regionIds: string[]) => void;
  clearSelection: () => void;
  
  // Batch operations for performance
  batchUpdateRegions: (updates: Array<{ id: string; updates: Partial<CanvasTextRegion> }>) => void;
}

interface UIStore {
  // UI state (separate from canvas for better performance)
  panels: {
    showProjectPanel: boolean;
    showTextEditPanel: boolean;
    showOCRPanel: boolean;
    showTranslationPanel: boolean;
    showGrid: boolean;
    showRulers: boolean;
  };
  
  layout: {
    sidebarWidth: number;
    rightPanelWidth: number;
  };
  
  // Actions
  togglePanel: (panel: keyof UIStore['panels']) => void;
  setPanelVisibility: (panel: keyof UIStore['panels'], visible: boolean) => void;
  setLayout: (layout: Partial<UIStore['layout']>) => void;
}

// Create optimized stores with middleware
export const useCanvasStore = create<CanvasStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      viewport: {
        zoom: 1,
        panX: 0,
        panY: 0,
      },
      
      interaction: {
        isDrawing: false,
        selectedTool: CanvasTool.SELECT,
        isDragging: false,
        isPanning: false,
      },
      
      image: null,
      
      setViewport: (viewport) =>
        set((state) => {
          Object.assign(state.viewport, viewport);
        }),
      
      setInteraction: (interaction) =>
        set((state) => {
          Object.assign(state.interaction, interaction);
        }),
      
      setImage: (image) =>
        set((state) => {
          state.image = image;
        }),
      
      resetViewport: () =>
        set((state) => {
          state.viewport = { zoom: 1, panX: 0, panY: 0 };
        }),
      
      zoomIn: () =>
        set((state) => {
          state.viewport.zoom = Math.min(state.viewport.zoom * 1.2, 5);
        }),
      
      zoomOut: () =>
        set((state) => {
          state.viewport.zoom = Math.max(state.viewport.zoom / 1.2, 0.1);
        }),
    }))
  )
);

export const useTextRegionsStore = create<TextRegionsStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      regions: [],
      selectedRegionIds: [],
      
      addRegion: (region) =>
        set((state) => {
          state.regions.push(region);
        }),
      
      updateRegion: (regionId, updates) =>
        set((state) => {
          const index = state.regions.findIndex(r => r.id === regionId);
          if (index !== -1) {
            Object.assign(state.regions[index], updates);
          }
        }),
      
      removeRegion: (regionId) =>
        set((state) => {
          state.regions = state.regions.filter(r => r.id !== regionId);
          state.selectedRegionIds = state.selectedRegionIds.filter(id => id !== regionId);
        }),
      
      selectRegion: (regionId) =>
        set((state) => {
          state.selectedRegionIds = [regionId];
        }),
      
      selectMultipleRegions: (regionIds) =>
        set((state) => {
          state.selectedRegionIds = regionIds;
        }),
      
      clearSelection: () =>
        set((state) => {
          state.selectedRegionIds = [];
        }),
      
      batchUpdateRegions: (updates) =>
        set((state) => {
          updates.forEach(({ id, updates: regionUpdates }) => {
            const index = state.regions.findIndex(r => r.id === id);
            if (index !== -1) {
              Object.assign(state.regions[index], regionUpdates);
            }
          });
        }),
    }))
  )
);

export const useUIStore = create<UIStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      panels: {
        showProjectPanel: true,
        showTextEditPanel: true,
        showOCRPanel: false,
        showTranslationPanel: false,
        showGrid: false,
        showRulers: false,
      },
      
      layout: {
        sidebarWidth: 320,
        rightPanelWidth: 320,
      },
      
      togglePanel: (panel) =>
        set((state) => {
          state.panels[panel] = !state.panels[panel];
        }),
      
      setPanelVisibility: (panel, visible) =>
        set((state) => {
          state.panels[panel] = visible;
        }),
      
      setLayout: (layout) =>
        set((state) => {
          Object.assign(state.layout, layout);
        }),
    }))
  )
);

// Performance monitoring utilities
export const createPerformanceMonitor = () => {
  let renderCount = 0;
  let lastRenderTime = performance.now();
  
  return {
    trackRender: () => {
      renderCount++;
      const now = performance.now();
      const timeSinceLastRender = now - lastRenderTime;
      lastRenderTime = now;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`Render #${renderCount}, Time since last: ${timeSinceLastRender.toFixed(2)}ms`);
      }
    },
    
    getRenderCount: () => renderCount,
    reset: () => {
      renderCount = 0;
      lastRenderTime = performance.now();
    }
  };
};
