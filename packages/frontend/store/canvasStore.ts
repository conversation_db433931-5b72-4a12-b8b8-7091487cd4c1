'use client';

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { CanvasState, CanvasTool, CanvasTextRegion, CanvasImageInfo } from '@/types/canvas';

// Single optimized store with transient update support
interface CanvasStore {
  // Viewport state (for transient updates during panning/zooming)
  viewport: {
    zoom: number;
    panX: number;
    panY: number;
  };

  // Tool and interaction state
  selectedTool: CanvasTool;
  isDrawing: boolean;

  // Image state
  image: CanvasImageInfo | null;

  // Text regions (separate from high-frequency viewport updates)
  textRegions: CanvasTextRegion[];
  selectedRegionIds: string[];

  // Actions
  setViewport: (viewport: Partial<CanvasStore['viewport']>) => void;
  setTool: (tool: CanvasTool) => void;
  setDrawing: (isDrawing: boolean) => void;
  setImage: (image: CanvasImageInfo | null) => void;

  // Region actions
  addRegion: (region: CanvasTextRegion) => void;
  updateRegion: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  removeRegion: (regionId: string) => void;
  selectRegion: (regionId: string) => void;

  // Optimized actions
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
}

// Create the optimized canvas store
export const useCanvasStore = create<CanvasStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    viewport: {
      zoom: 1,
      panX: 0,
      panY: 0,
    },

    selectedTool: CanvasTool.SELECT,
    isDrawing: false,
    image: null,
    textRegions: [],
    selectedRegionIds: [],

    // Actions
    setViewport: (viewport) =>
      set((state) => ({
        viewport: { ...state.viewport, ...viewport }
      })),

    setTool: (tool) =>
      set({ selectedTool: tool }),

    setDrawing: (isDrawing) =>
      set({ isDrawing }),

    setImage: (image) =>
      set({ image }),

    // Region actions
    addRegion: (region) =>
      set((state) => ({
        textRegions: [...state.textRegions, region]
      })),

    updateRegion: (regionId, updates) =>
      set((state) => ({
        textRegions: state.textRegions.map(region =>
          region.id === regionId ? { ...region, ...updates } : region
        )
      })),

    removeRegion: (regionId) =>
      set((state) => ({
        textRegions: state.textRegions.filter(region => region.id !== regionId),
        selectedRegionIds: state.selectedRegionIds.filter(id => id !== regionId)
      })),

    selectRegion: (regionId) =>
      set({ selectedRegionIds: [regionId] }),

    // Zoom actions
    zoomIn: () =>
      set((state) => ({
        viewport: {
          ...state.viewport,
          zoom: Math.min(state.viewport.zoom * 1.2, 5)
        }
      })),

    zoomOut: () =>
      set((state) => ({
        viewport: {
          ...state.viewport,
          zoom: Math.max(state.viewport.zoom / 1.2, 0.1)
        }
      })),

    resetZoom: () =>
      set((state) => ({
        viewport: { ...state.viewport, zoom: 1 }
      })),
  }))
);

// Transient update utilities for high-frequency canvas operations
export const createTransientUpdater = () => {
  const store = useCanvasStore;

  return {
    // Update viewport without triggering React re-renders
    updateViewportTransient: (viewport: Partial<CanvasStore['viewport']>) => {
      const currentState = store.getState();
      const newViewport = { ...currentState.viewport, ...viewport };

      // Update store state directly (this will trigger subscriptions but not React re-renders)
      store.setState({ viewport: newViewport }, false, 'transient-viewport-update');
    },

    // Subscribe to viewport changes for external canvas updates
    subscribeToViewport: (callback: (viewport: CanvasStore['viewport']) => void) => {
      return store.subscribe(
        (state) => state.viewport,
        callback,
        { fireImmediately: true }
      );
    },

    // Subscribe to tool changes
    subscribeToTool: (callback: (tool: CanvasTool) => void) => {
      return store.subscribe(
        (state) => state.selectedTool,
        callback
      );
    }
  };
};

// Performance monitoring for development
export const performanceMonitor = {
  renderCount: 0,
  lastRenderTime: performance.now(),

  trackRender: (componentName: string) => {
    if (process.env.NODE_ENV === 'development') {
      performanceMonitor.renderCount++;
      const now = performance.now();
      const timeSinceLastRender = now - performanceMonitor.lastRenderTime;
      performanceMonitor.lastRenderTime = now;

      console.log(`${componentName} render #${performanceMonitor.renderCount}, Time: ${timeSinceLastRender.toFixed(2)}ms`);
    }
  },

  reset: () => {
    performanceMonitor.renderCount = 0;
    performanceMonitor.lastRenderTime = performance.now();
  }
};
