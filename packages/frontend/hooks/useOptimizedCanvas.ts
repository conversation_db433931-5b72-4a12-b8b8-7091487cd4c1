'use client';

import { useRef, useEffect, useCallback } from 'react';
import { useShallow } from 'zustand/react/shallow';
import * as fabric from 'fabric';
import { useCanvasStore, createTransientUpdater, performanceMonitor } from '@/store/canvasStore';
import { CanvasTextRegion, CanvasTool } from '@/types/canvas';

interface UseOptimizedCanvasOptions {
  onRegionCreate?: (region: Partial<CanvasTextRegion>) => void;
  onRegionUpdate?: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  onRegionDelete?: (regionId: string) => void;
  onRegionSelect?: (regionId: string) => void;
}

export const useOptimizedCanvas = (options: UseOptimizedCanvasOptions = {}) => {
  const canvasRef = useRef<fabric.Canvas | null>(null);
  const imageRef = useRef<fabric.FabricImage | null>(null);

  // Use shallow selectors to prevent unnecessary re-renders
  const viewport = useCanvasStore(useShallow(state => state.viewport));
  const interaction = useCanvasStore(useShallow(state => state.interaction));
  const image = useCanvasStore(state => state.image);

  // Canvas actions
  const setViewport = useCanvasStore(state => state.setViewport);
  const setInteraction = useCanvasStore(state => state.setInteraction);
  const zoomIn = useCanvasStore(state => state.zoomIn);
  const zoomOut = useCanvasStore(state => state.zoomOut);
  const resetViewport = useCanvasStore(state => state.resetViewport);

  // Text regions actions
  const addRegion = useTextRegionsStore(state => state.addRegion);
  const updateRegion = useTextRegionsStore(state => state.updateRegion);
  const batchUpdateRegions = useTextRegionsStore(state => state.batchUpdateRegions);

  // Batch updater for region coordinates during dragging
  const regionBatchUpdater = useRef(
    new BatchUpdater<{ id: string; updates: Partial<CanvasTextRegion> }>(
      (updates) => batchUpdateRegions(updates),
      16 // Update at 60fps
    )
  );

  // Throttled viewport updates for smooth panning
  const throttledViewportUpdate = useRafThrottledCallback(
    (updates: Partial<typeof viewport>) => {
      setViewport(updates);
    },
    [setViewport]
  );

  // Debounced region updates for final position
  const debouncedRegionUpdate = useThrottledCallback(
    (regionId: string, updates: Partial<CanvasTextRegion>) => {
      updateRegion(regionId, updates);
      options.onRegionUpdate?.(regionId, updates);
    },
    100, // Debounce final updates
    [updateRegion, options.onRegionUpdate]
  );

  // Transient viewport updates (don't trigger React re-renders)
  const updateViewportTransient = useCallback((updates: Partial<typeof viewport>) => {
    // Use Zustand's subscribe for transient updates
    const currentViewport = useCanvasStore.getState().viewport;
    const newViewport = { ...currentViewport, ...updates };

    // Update canvas directly without triggering React re-renders
    if (canvasRef.current) {
      const canvas = canvasRef.current;
      const vpt = canvas.viewportTransform;
      if (vpt) {
        vpt[4] = newViewport.panX;
        vpt[5] = newViewport.panY;
        canvas.setZoom(newViewport.zoom);
        canvas.requestRenderAll();
      }
    }

    // Throttled state update for other components that need it
    throttledViewportUpdate(updates);
  }, [throttledViewportUpdate]);

  // Initialize canvas
  const initializeCanvas = useCallback((canvas: fabric.Canvas) => {
    canvasRef.current = canvas;

    // Set up optimized event handlers
    canvas.on('mouse:wheel', (opt) => {
      const delta = opt.e.deltaY;
      let zoom = canvas.getZoom();
      zoom *= 0.999 ** delta;

      if (zoom > 5) zoom = 5;
      if (zoom < 0.1) zoom = 0.1;

      const point = new fabric.Point(opt.e.offsetX, opt.e.offsetY);
      canvas.zoomToPoint(point, zoom);

      // Transient update - no React re-render
      const vpt = canvas.viewportTransform;
      if (vpt) {
        updateViewportTransient({
          zoom,
          panX: vpt[4],
          panY: vpt[5]
        });
      }

      opt.e.preventDefault();
      opt.e.stopPropagation();
    });

    canvas.on('mouse:down', (opt) => {
      const isAltPressed = opt.e?.altKey;
      const isSpacePressed = opt.e?.code === 'Space';
      const shouldPan = interaction.selectedTool === CanvasTool.PAN || isAltPressed || isSpacePressed;

      if (shouldPan) {
        setInteraction({ isDragging: true, isPanning: true });
        canvas.selection = false;
        (canvas as any).lastPosX = opt.e?.clientX || 0;
        (canvas as any).lastPosY = opt.e?.clientY || 0;
      }
    });

    canvas.on('mouse:move', (opt) => {
      if (interaction.isDragging && interaction.isPanning) {
        const vpt = canvas.viewportTransform;
        if (vpt && opt.e) {
          const deltaX = opt.e.clientX - ((canvas as any).lastPosX || 0);
          const deltaY = opt.e.clientY - ((canvas as any).lastPosY || 0);

          vpt[4] += deltaX;
          vpt[5] += deltaY;
          canvas.requestRenderAll();

          (canvas as any).lastPosX = opt.e.clientX;
          (canvas as any).lastPosY = opt.e.clientY;

          // Transient update during panning
          updateViewportTransient({
            panX: vpt[4],
            panY: vpt[5]
          });
        }
      }
    });

    canvas.on('mouse:up', () => {
      setInteraction({ isDragging: false, isPanning: false });
      canvas.selection = interaction.selectedTool === CanvasTool.SELECT;
    });

    // Optimized object event handlers
    canvas.on('object:moving', (opt) => {
      const obj = opt.target;
      if (obj && (obj as any).data?.regionId) {
        const regionId = (obj as any).data.regionId;
        const bounds = obj.getBoundingRect();

        // Batch update during movement (no immediate React re-render)
        regionBatchUpdater.current.add({
          id: regionId,
          updates: {
            x: bounds.left / canvas.getWidth(),
            y: bounds.top / canvas.getHeight(),
            width: bounds.width / canvas.getWidth(),
            height: bounds.height / canvas.getHeight()
          }
        });
      }
    });

    canvas.on('object:modified', (opt) => {
      const obj = opt.target;
      if (obj && (obj as any).data?.regionId) {
        const regionId = (obj as any).data.regionId;
        const bounds = obj.getBoundingRect();

        // Final update when movement is complete
        debouncedRegionUpdate(regionId, {
          x: bounds.left / canvas.getWidth(),
          y: bounds.top / canvas.getHeight(),
          width: bounds.width / canvas.getWidth(),
          height: bounds.height / canvas.getHeight()
        });

        // Flush any pending batch updates
        regionBatchUpdater.current.flush();
      }
    });

    canvas.on('selection:created', (opt) => {
      const activeObject = opt.selected?.[0];
      if (activeObject && (activeObject as any).data?.regionId) {
        options.onRegionSelect?.((activeObject as any).data.regionId);
      }
    });

  }, [interaction.selectedTool, interaction.isDragging, interaction.isPanning, setInteraction, updateViewportTransient, debouncedRegionUpdate, options.onRegionSelect]);

  // Load image
  const loadImage = useCallback(async (imageUrl: string) => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;

    try {
      const img = await fabric.FabricImage.fromURL(imageUrl);

      // Remove existing image
      if (imageRef.current) {
        canvas.remove(imageRef.current);
      }

      // Add new image
      img.set({
        left: 0,
        top: 0,
        selectable: false,
        evented: false
      });

      canvas.add(img);
      canvas.sendToBack(img);
      imageRef.current = img;

      // Update image info in store
      useCanvasStore.getState().setImage({
        width: img.width || 0,
        height: img.height || 0,
        naturalWidth: img.width || 0,
        naturalHeight: img.height || 0
      });

      canvas.renderAll();

    } catch (error) {
      console.error('Failed to load image:', error);
    }
  }, []);

  // Set tool
  const setTool = useCallback((tool: CanvasTool) => {
    if (!canvasRef.current) return;

    canvasRef.current.selection = tool === CanvasTool.SELECT;
    setInteraction({ selectedTool: tool });
  }, [setInteraction]);

  // Cleanup
  useEffect(() => {
    return () => {
      regionBatchUpdater.current.clear();
    };
  }, []);

  return {
    // State (optimized selectors)
    viewport,
    interaction,
    image,

    // Canvas management
    initializeCanvas,
    loadImage,

    // Controls
    zoomIn,
    zoomOut,
    resetViewport,
    setTool,

    // Refs
    canvasRef,
    imageRef
  };
};
