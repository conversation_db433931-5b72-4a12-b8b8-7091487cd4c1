'use client';

import { useRef, useEffect, useCallback } from 'react';
import * as fabric from 'fabric';
import { useCanvasStore, createTransientUpdater } from '@/store/canvasStore';
import { throttle, debounce, rafThrottle } from '@/utils/performance';
import { CanvasTextRegion, CanvasTool } from '@/types/canvas';

interface UseCanvasOptions {
  onRegionCreate?: (region: Partial<CanvasTextRegion>) => void;
  onRegionUpdate?: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  onRegionDelete?: (regionId: string) => void;
  onRegionSelect?: (regionId: string) => void;
}

export const useCanvas = (options: UseCanvasOptions = {}) => {
  const canvasRef = useRef<fabric.Canvas | null>(null);
  const imageRef = useRef<fabric.FabricImage | null>(null);
  const transientUpdater = useRef(createTransientUpdater());
  const lastPanPosition = useRef({ x: 0, y: 0 });
  const regionUpdateQueue = useRef<Map<string, Partial<CanvasTextRegion>>>(new Map());

  // Use selective subscriptions to prevent unnecessary re-renders
  const selectedTool = useCanvasStore((state) => state.selectedTool);
  const image = useCanvasStore((state) => state.image);

  // Get store actions with proper typing
  const setToolAction = useCanvasStore((state) => state.setTool);
  const setDrawing = useCanvasStore((state) => state.setDrawing);
  const zoomIn = useCanvasStore((state) => state.zoomIn);
  const zoomOut = useCanvasStore((state) => state.zoomOut);
  const resetZoom = useCanvasStore((state) => state.resetZoom);
  const updateRegion = useCanvasStore((state) => state.updateRegion);

  // Performance-optimized throttled functions
  const throttledViewportUpdate = useRef(
    rafThrottle((viewport: { panX: number; panY: number; zoom?: number }) => {
      transientUpdater.current.updateViewportTransient(viewport);
    })
  );

  const debouncedRegionUpdate = useRef(
    debounce((regionId: string, updates: Partial<CanvasTextRegion>) => {
      updateRegion(regionId, updates);
      options.onRegionUpdate?.(regionId, updates);
    }, 100)
  );

  const batchedRegionUpdates = useRef(
    throttle(() => {
      const updates = Array.from(regionUpdateQueue.current.entries());
      regionUpdateQueue.current.clear();

      updates.forEach(([regionId, regionUpdates]) => {
        updateRegion(regionId, regionUpdates);
      });
    }, 16) // 60fps
  );

  // High-performance canvas initialization with optimized event handlers
  const initializeCanvas = useCallback((canvas: fabric.Canvas) => {
    canvasRef.current = canvas;

    // Mouse wheel zoom with transient updates
    canvas.on('mouse:wheel', (opt) => {
      const delta = opt.e.deltaY;
      let zoom = canvas.getZoom();
      zoom *= 0.999 ** delta;

      if (zoom > 5) zoom = 5;
      if (zoom < 0.1) zoom = 0.1;

      const point = new fabric.Point(opt.e.offsetX, opt.e.offsetY);
      canvas.zoomToPoint(point, zoom);

      // Transient update - no React re-render during zoom
      const vpt = canvas.viewportTransform;
      if (vpt) {
        throttledViewportUpdate.current({
          panX: vpt[4],
          panY: vpt[5],
          zoom
        });
      }

      opt.e.preventDefault();
      opt.e.stopPropagation();
    });

    // Optimized panning with minimal state updates
    canvas.on('mouse:down', (opt) => {
      const isAltPressed = opt.e?.altKey;
      const shouldPan = selectedTool === CanvasTool.PAN || isAltPressed;

      if (shouldPan) {
        setDrawing(true);
        canvas.selection = false;
        lastPanPosition.current = { x: (opt.e as MouseEvent)?.clientX || 0, y: (opt.e as MouseEvent)?.clientY || 0 };
        (canvas as any).isDragging = true;
        (canvas as any).isPanMode = true;
      }
    });

    // High-performance mouse move with transient updates
    canvas.on('mouse:move', (opt) => {
      if ((canvas as any).isDragging && (canvas as any).isPanMode) {
        const vpt = canvas.viewportTransform;
        if (vpt && opt.e) {
          const currentX = (opt.e as MouseEvent).clientX || 0;
          const currentY = (opt.e as MouseEvent).clientY || 0;

          const deltaX = currentX - lastPanPosition.current.x;
          const deltaY = currentY - lastPanPosition.current.y;

          vpt[4] += deltaX;
          vpt[5] += deltaY;
          canvas.requestRenderAll();

          lastPanPosition.current = { x: currentX, y: currentY };

          // Transient update - no React re-render during panning
          throttledViewportUpdate.current({
            panX: vpt[4],
            panY: vpt[5]
          });
        }
      }
    });

    // Mouse up - end panning
    canvas.on('mouse:up', () => {
      (canvas as any).isDragging = false;
      (canvas as any).isPanMode = false;
      canvas.selection = selectedTool === CanvasTool.SELECT;
      setDrawing(false);
    });

    // Optimized object event handlers for text regions
    canvas.on('object:moving', (opt) => {
      const obj = opt.target;
      if (obj && (obj as any).data?.regionId) {
        const regionId = (obj as any).data.regionId;
        const bounds = obj.getBoundingRect();

        // Queue update instead of immediate state change
        regionUpdateQueue.current.set(regionId, {
          x: bounds.left / canvas.getWidth(),
          y: bounds.top / canvas.getHeight(),
          width: bounds.width / canvas.getWidth(),
          height: bounds.height / canvas.getHeight()
        });

        // Batch process queued updates
        batchedRegionUpdates.current();
      }
    });

    canvas.on('object:modified', (opt) => {
      const obj = opt.target;
      if (obj && (obj as any).data?.regionId) {
        const regionId = (obj as any).data.regionId;
        const bounds = obj.getBoundingRect();

        // Final update when movement is complete
        debouncedRegionUpdate.current(regionId, {
          x: bounds.left / canvas.getWidth(),
          y: bounds.top / canvas.getHeight(),
          width: bounds.width / canvas.getWidth(),
          height: bounds.height / canvas.getHeight()
        });
      }
    });

    canvas.on('selection:created', (opt) => {
      const activeObject = opt.selected?.[0];
      if (activeObject && (activeObject as any).data?.regionId) {
        options.onRegionSelect?.((activeObject as any).data.regionId);
      }
    });

  }, [selectedTool, setDrawing, options.onRegionSelect]);

  // Load image with performance optimization
  const loadImage = useCallback(async (imageUrl: string) => {
    if (!canvasRef.current) {
      return;
    }

    const canvas = canvasRef.current;

    try {
      // Remove existing image first
      if (imageRef.current) {
        canvas.remove(imageRef.current);
        imageRef.current = null;
      }

      const img = await fabric.FabricImage.fromURL(imageUrl);

      // Configure image properties
      img.set({
        left: 0,
        top: 0,
        selectable: false,
        evented: false
      });

      // Add image to canvas
      canvas.add(img);
      imageRef.current = img;

      // Update image info in store
      useCanvasStore.getState().setImage({
        originalWidth: img.width || 0,
        originalHeight: img.height || 0,
        displayWidth: img.width || 0,
        displayHeight: img.height || 0,
        scaleX: 1,
        scaleY: 1,
        imageUrl: imageUrl
      });

      // Force canvas to render
      canvas.renderAll();

    } catch (error) {
      console.error('Failed to load image:', error);
      // Clear image info in store on error
      useCanvasStore.getState().setImage(null);
    }
  }, []);

  // Set tool with canvas optimization
  const setTool = useCallback((tool: CanvasTool) => {
    if (!canvasRef.current) return;

    canvasRef.current.selection = tool === CanvasTool.SELECT;
    setToolAction(tool);
  }, [setToolAction]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      regionUpdateQueue.current.clear();
    };
  }, []);

  return {
    // State (optimized selectors)
    selectedTool,
    image,

    // Canvas management
    initializeCanvas,
    loadImage,

    // Controls
    zoomIn,
    zoomOut,
    resetZoom,
    setTool,

    // Refs
    canvasRef,
    imageRef
  };
};
